#include "StereoReconstructionService.hpp"
#include "../Utils/utf8_utils.hpp"
#include "../Utils/DebugConfig.hpp"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <vector>

// Helper function to calculate dot product
double dot_product(const std::vector<double>& a, const std::vector<double>& b) {
    double result = 0.0;
    for (size_t i = 0; i < a.size(); ++i) {
        result += a[i] * b[i];
    }
    return result;
}

StereoReconstructionService::StereoReconstructionService(
    std::shared_ptr<SharedData> shared_data,
    Services::DataLoggingService* data_logging_service,
    int image_width, 
    int image_height
) : m_sharedData(std::move(shared_data)), 
    m_dataLoggingService(data_logging_service),
    m_wasBallDetectedLastFrame(false) {
    
    // 初始化双目视觉系统
    m_dualEye = std::make_unique<DUE::C_DualEye>(image_width, image_height);
    
    // UTF8Utils::println("🔧 立体视觉重建服务已初始化 (" +
    //                   std::to_string(image_width) + "x" + std::to_string(image_height) + ")");
}

void StereoReconstructionService::initializeROIFeatures() {
    m_roiPredictor = std::make_unique<DynamicROIPredictor>();
    m_roiEnabled = true;
    m_sharedData->setROIProcessingMode(true);
    UTF8Utils::println("🎯 ROI预测功能已启用");
}

void StereoReconstructionService::setROIEnabled(bool enabled) {
    m_roiEnabled = enabled;
    m_sharedData->setROIProcessingMode(enabled);
    if (enabled) {
        UTF8Utils::println("🎯 ROI预测功能已启用");
    } else {
        UTF8Utils::println("🎯 ROI预测功能已禁用");
    }
}

void StereoReconstructionService::calculateAndStoreSpeed(const BallPosition3D& latest_position) {
    auto current_time = std::chrono::high_resolution_clock::now();

    // 检测帧重复：如果新位置与最后一个位置过于接近，可能是重复帧
    if (!m_positionHistory.empty()) {
        const auto& last_pos = m_positionHistory.back().point;
        const auto& new_pos = latest_position.world_position;

        float position_change = std::sqrt(
            std::pow(new_pos.x - last_pos.x, 2) +
            std::pow(new_pos.y - last_pos.y, 2) +
            std::pow(new_pos.z - last_pos.z, 2)
        );

        // 如果位置变化小于1mm，可能是重复帧
        if (position_change < 0.001f) {
            auto time_diff = std::chrono::duration<double>(current_time - m_positionHistory.back().timestamp).count();
            DEBUG_BALL_SPEED("检测到可能的重复帧 - 位置变化: " +
                           std::to_string(position_change * 1000) + "mm, 时间间隔: " +
                           std::to_string(time_diff * 1000) + "ms");

            // 如果时间间隔也很小（<2ms），跳过此帧
            if (time_diff < 0.002) {
                DEBUG_BALL_SPEED("跳过重复帧，保持当前球速");
                // return; // 暂时注释掉，让速度计算继续进行
            }
        }
    }

    // 1. Add new point to history
    m_positionHistory.push_back({current_time, latest_position.world_position});

    // 2. Maintain history size
    if (m_positionHistory.size() > m_historySize) {
        m_positionHistory.pop_front();
    }

    // 3. Check if we have enough data for SG filter (使用基于210FPS优化的窗口大小)
    const int optimized_window_size = m_sgWindowSize;  // 7点，基于分析的最优参数
    if (m_positionHistory.size() < optimized_window_size) {
        // 优化：减少调试输出，只在开发模式下显示
        if (DebugConfig::enable_ball_speed_debug) {
            DEBUG_BALL_SPEED("历史数据不足: " + std::to_string(m_positionHistory.size()) +
                            "/" + std::to_string(optimized_window_size) + " 点，设置速度为0");
        }
        m_sharedData->setBallSpeed(0.0);
        return;
    }

    // Use the most recent `optimized_window_size` points for calculation
    auto start_it = m_positionHistory.end() - optimized_window_size;

    std::vector<double> t_data, x_data, y_data, z_data;
    std::vector<double> dt_values; // 存储实际时间间隔
    t_data.reserve(optimized_window_size);
    x_data.reserve(optimized_window_size);
    y_data.reserve(optimized_window_size);
    z_data.reserve(optimized_window_size);

    auto first_timestamp = start_it->timestamp;
    for (auto it = start_it; it != m_positionHistory.end(); ++it) {
        t_data.push_back(std::chrono::duration<double>(it->timestamp - first_timestamp).count());
        x_data.push_back(it->point.x);
        y_data.push_back(it->point.y);
        z_data.push_back(it->point.z);
    }

    // 计算实际相邻帧之间的时间间隔
    for (auto it = start_it + 1; it != m_positionHistory.end(); ++it) {
        auto dt = std::chrono::duration<double>(it->timestamp - (it-1)->timestamp).count();
        dt_values.push_back(dt);
    }

    try {
        // 验证时间间隔的有效性
        if (dt_values.empty()) {
            if (DebugConfig::enable_ball_speed_debug) {
                DEBUG_BALL_SPEED("时间间隔计算失败，设置速度为0");
            }
            m_sharedData->setBallSpeed(0.0);
            // return; // 暂时注释掉，让速度计算继续进行
        }

        // 计算时间间隔统计信息
        double min_dt = *std::min_element(dt_values.begin(), dt_values.end());
        double max_dt = *std::max_element(dt_values.begin(), dt_values.end());
        double sum_dt = std::accumulate(dt_values.begin(), dt_values.end(), 0.0);
        double avg_dt = sum_dt / dt_values.size();

        // 智能时间间隔处理策略（基于处理时间戳的旧逻辑，保留兼容性）
        const double MAX_NORMAL_INTERVAL = 0.2;      // 200ms - 正常处理间隔
        const double MAX_ACCEPTABLE_INTERVAL = 1.0;   // 1秒 - 可接受的间隔
        const double MAX_REASONABLE_INTERVAL = 3.0;   // 3秒 - 合理的重连间隔

        // 统计时间间隔分布
        int normal_intervals = 0;
        int acceptable_intervals = 0;
        int problematic_intervals = 0;

        for (double dt : dt_values) {
            if (dt <= MAX_NORMAL_INTERVAL) normal_intervals++;
            else if (dt <= MAX_ACCEPTABLE_INTERVAL) acceptable_intervals++;
            else problematic_intervals++;
        }

        // 如果大部分时间间隔都是异常的，说明这是球重新出现
        if (problematic_intervals > dt_values.size() / 2) {
            DEBUG_BALL_SPEED("检测到球重新出现 - 异常间隔: " + std::to_string(problematic_intervals) +
                           "/" + std::to_string(dt_values.size()) + ", 最大: " +
                           std::to_string(max_dt * 1000) + "ms, 重新开始轨迹跟踪");

            // 保留最近的2个点，丢弃旧的异常数据
            if (m_positionHistory.size() >= 2) {
                auto recent_points = std::vector<TimedPoint3f>(
                    m_positionHistory.end() - 2, m_positionHistory.end()
                );
                // m_positionHistory.clear(); // 暂时注释掉，防止历史数据清空
                for (const auto& point : recent_points) {
                    m_positionHistory.push_back(point);
                }
                DEBUG_BALL_SPEED("保留最近2个检测点，继续跟踪");
            } else {
                // m_positionHistory.clear(); // 暂时注释掉，防止历史数据清空
                m_positionHistory.push_back({current_time, latest_position.world_position});
                DEBUG_BALL_SPEED("历史数据不足，重新开始");
            }
            m_sharedData->setBallSpeed(0.0);
            // return; // 暂时注释掉，让速度计算继续进行
        }

        // 如果只有少数异常间隔，使用中位数时间间隔进行计算
        if (problematic_intervals > 0 && problematic_intervals <= dt_values.size() / 2) {
            // 计算中位数时间间隔，排除异常值
            std::vector<double> normal_dt_values;
            for (double dt : dt_values) {
                if (dt <= MAX_ACCEPTABLE_INTERVAL) {
                    normal_dt_values.push_back(dt);
                }
            }

            if (!normal_dt_values.empty()) {
                std::sort(normal_dt_values.begin(), normal_dt_values.end());
                double median_dt = normal_dt_values[normal_dt_values.size() / 2];
                avg_dt = median_dt;  // 使用中位数替代平均值

                DEBUG_BALL_SPEED("使用中位数时间间隔: " + std::to_string(median_dt * 1000) +
                               "ms (排除" + std::to_string(problematic_intervals) + "个异常值)");
            }
        }

        // 正常情况的调试信息
        if (normal_intervals == dt_values.size()) {
            DEBUG_BALL_SPEED("时间间隔正常 - 平均: " + std::to_string(avg_dt * 1000) + "ms");
        }

        if (avg_dt < 1e-6) { // Avoid division by zero
            DEBUG_BALL_SPEED("平均时间间隔过小: " + std::to_string(avg_dt) + "s，设置速度为0");
            m_sharedData->setBallSpeed(0.0);
            // return; // 暂时注释掉，让速度计算继续进行
        }

        // 4. Compute SG coefficients for the 1st derivative (使用基于分析的最优参数)
        const int optimized_poly_order = m_sgPolyOrder;   // 2阶多项式，可检测加速度变化
        auto coeffs = SignalProcessing::compute_sg_coeffs(optimized_window_size, optimized_poly_order, 1);

        // 5. Apply filter to get derivatives (velocity components)
        // The derivative is applied to the central point of the window
        double vx = dot_product(coeffs, x_data) / avg_dt;
        double vy = dot_product(coeffs, y_data) / avg_dt;
        double vz = dot_product(coeffs, z_data) / avg_dt;

        // 6. Calculate speed magnitude and store it
        double speed = std::sqrt(vx * vx + vy * vy + vz * vz);

        // 多级异常速度检测（基于优化后的阈值）
        if (speed > SPEED_CRITICAL_THRESHOLD) {
            DEBUG_BALL_SPEED("🚨 严重异常速度: " + std::to_string(speed) + " m/s，明确的系统错误");
        } else if (speed > SPEED_ANOMALY_THRESHOLD) {
            DEBUG_BALL_SPEED("⚠️ 异常速度: " + std::to_string(speed) + " m/s，可能的计算错误");
        } else if (speed > SPEED_WARNING_THRESHOLD) {
            DEBUG_BALL_SPEED("⚡ 警告速度: " + std::to_string(speed) + " m/s，超出常见范围");
        }

        // 详细调试信息仅在开发调试模式下显示
        if (DebugConfig::enable_ball_speed_debug) {
            DEBUG_BALL_SPEED("优化SG滤波器速度计算 - 窗口:" + std::to_string(optimized_window_size) + 
                            "点, 阶数:" + std::to_string(optimized_poly_order) + 
                            ", 时间跨度:" + std::to_string(optimized_window_size * avg_dt * 1000) + "ms" +
                            ", 平均间隔:" + std::to_string(avg_dt * 1000) + "ms" +
                            ", 速度:" + std::to_string(speed) + "m/s");
        }

        m_sharedData->setBallSpeed(speed);

        // 注释：数据记录已移至独立的数据记录路径，不再在此处重复记录
        // 这样可以避免数据记录与三维重建成功与否的耦合

        // === 新增：ROI预测逻辑 ===
        if (m_roiEnabled && m_roiPredictor) {
            updateROIPredictionsUsingDualEye(latest_position);
        }

    } catch (const std::exception& e) {
        UTF8Utils::println(std::string("❌ SG Filter speed calculation error: ") + e.what());
        m_sharedData->setBallSpeed(0.0);
    }
}

bool StereoReconstructionService::processDetections(
    const std::map<std::string, std::vector<Yolo::Detection>>& left_detections,
    const std::map<std::string, std::vector<Yolo::Detection>>& right_detections,
    std::chrono::milliseconds timestamp
) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        std::vector<BallPosition3D> ball_positions;
        
        // 遍历所有目标类别，进行跨视图匹配
        for (const auto& class_name : m_targetClasses) {
            // 使用DualEye库进行特征匹配
            auto matched_pairs = DUE::classifyMultiple(
                left_detections, 
                right_detections, 
                class_name, 
                m_matchingThreshold
            );

            if (!matched_pairs.empty()) {
                // 进行三维重建，计算世界坐标
                auto world_points = m_dualEye->calP3inWorld(matched_pairs);
                
                // 将结果转换为BallPosition3D格式
                for (size_t i = 0; i < world_points.size(); ++i) {
                    const auto& world_point = world_points[i];
                    
                    // 计算置信度（取匹配对中的较小值）
                    float confidence = std::min<float>(
                        matched_pairs[i].confLeft, 
                        matched_pairs[i].confRight
                    );
                    
                    ball_positions.emplace_back(
                        static_cast<int>(ball_positions.size()), // 球的ID
                        world_point,
                        confidence,
                        timestamp
                    );
                }

                // --- 新增: 更新2D轨迹 ---
                // 在这里 matched_pairs 是可见的，并且与重建的球一一对应
                for (const auto& ball_match : matched_pairs) {
                    // 假设相机ID 1 是左, 2 是右
                    m_2d_trajectories[1].push_back(ball_match.uvLeft);
                    if (m_2d_trajectories[1].size() > m_max2dTrajectorySize) {
                        m_2d_trajectories[1].pop_front();
                    }

                    m_2d_trajectories[2].push_back(ball_match.uvRight);
                    if (m_2d_trajectories[2].size() > m_max2dTrajectorySize) {
                        m_2d_trajectories[2].pop_front();
                    }
                }
            }
        }
        
        // 将结果存储到SharedData中
        m_sharedData->setBallPositions3D(ball_positions);

        // 如果成功重建，计算并存储速度，并更新轨迹
        if (!ball_positions.empty()) {
            // 统计成功的3D重建次数
            m_sharedData->increment3DReconstructionCount();
            // 我们假设追踪列表中的第一个球
            calculateAndStoreSpeed(ball_positions[0]);

            // --- 新增：更新轨迹 ---
            if (!m_wasBallDetectedLastFrame) {
                // 如果上一帧没有球，而这一帧有，说明是新轨迹的开始
                m_currentTrajectory.clear();
                m_2d_trajectories.clear(); // 清空2D轨迹
            }
            m_wasBallDetectedLastFrame = true;

            m_currentTrajectory.push_back(ball_positions[0]);

            // --- 新增: 更新2D轨迹 ---
            // 我们需要原始的匹配对来获取2D坐标
            // --- 此处代码块已被移动到正确的位置 ---

            // 保持轨迹队列长度
            if (m_currentTrajectory.size() > m_maxTrajectorySize) {
                m_currentTrajectory.erase(m_currentTrajectory.begin());
            }

            // 将更新后的轨迹存入共享数据
            m_sharedData->setTrajectory(m_currentTrajectory);
            m_sharedData->set2dTrajectories(m_2d_trajectories); // 更新2D轨迹到SharedData
            // --------------------

        } else {
            // 没有检测到球时的智能处理策略
            auto current_time = std::chrono::high_resolution_clock::now();

            // 检查历史记录中最后一个点的时间
            if (!m_positionHistory.empty()) {
                auto last_detection_time = m_positionHistory.back().timestamp;
                auto time_since_last_detection = std::chrono::duration<double>(current_time - last_detection_time).count();

                // 基于高精度检测的优化球丢失容忍时间
                if (time_since_last_detection > BALL_LOSS_TOLERANCE) {
                    DEBUG_BALL_SPEED("球丢失超过" + std::to_string(BALL_LOSS_TOLERANCE) +
                                   "秒，清空历史记录。丢失时间: " +
                                   std::to_string(time_since_last_detection * 1000) + "ms");
                    // m_positionHistory.clear(); // 暂时注释掉，防止历史数据清空
                } else if (time_since_last_detection > BALL_LOSS_WARNING) {
                    DEBUG_BALL_SPEED("⚠️ 球丢失警告 " + std::to_string(time_since_last_detection * 1000) +
                                   "ms，保留历史记录等待重新检测");
                } else {
                    DEBUG_BALL_SPEED("球暂时丢失 " + std::to_string(time_since_last_detection * 1000) +
                                   "ms，保留历史记录等待重新检测");
                }
            }

            m_sharedData->setBallSpeed(0.0);
            m_wasBallDetectedLastFrame = false;
            // 当球消失时，我们暂时不清除轨迹，以便前端可以继续显示最后一段路径。
            // 只有当新的轨迹开始时（见上文）才清除。
        }
        
        // 更新统计信息
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        updateStatistics(!ball_positions.empty(), duration.count() / 1000.0);
        
        return !ball_positions.empty();
        
    } catch (const std::exception& e) {
        UTF8Utils::println("❌ 三维重建处理错误: " + std::string(e.what()));
        
        // 更新失败统计
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        updateStatistics(false, duration.count() / 1000.0);
        
        return false;
    }
}

bool StereoReconstructionService::processLatestDetections() {
    try {
        // 从SharedData获取最新的立体检测结果（带时间戳）
        auto [left_detection_with_ts, right_detection_with_ts] = m_sharedData->getStereoDetectionsWithTimestamp(1, 2);
        
        // 检查是否有有效数据
        if (left_detection_with_ts.detections.empty() || right_detection_with_ts.detections.empty()) {
            // 调试信息: 清楚地表明为何跳过处理
            if (left_detection_with_ts.detections.empty() && right_detection_with_ts.detections.empty()) {
                // 仅在两个都为空时安静地跳过，这是正常情况
            } else {
                 UTF8Utils::println("[调试] 跳过三维重建：仅单侧相机有检测结果 (左: " + 
                                   std::to_string(left_detection_with_ts.detections.size()) + ", 右: " + std::to_string(right_detection_with_ts.detections.size()) + ")");
                 
            }
            return false;
        }
        
        // 验证时间戳一致性（双目相机应该大致同时采集）
        auto time_diff = std::abs(std::chrono::duration<double>(
            left_detection_with_ts.capture_time - right_detection_with_ts.capture_time
        ).count());
        
        // 定期输出时间戳差异统计（每100次检查输出一次）
        static int sync_check_counter = 0;
        static double total_time_diff = 0.0;
        static double max_time_diff = 0.0;

        sync_check_counter++;
        total_time_diff += time_diff;
        max_time_diff = std::max(max_time_diff, time_diff);

        if (sync_check_counter % 100 == 0) {
            double avg_time_diff = total_time_diff / 100;
            UTF8Utils::println("[同步统计] 平均时间戳差异: " + std::to_string(avg_time_diff * 1000) +
                             "ms, 最大差异: " + std::to_string(max_time_diff * 1000) + "ms");
            total_time_diff = 0.0;
            max_time_diff = 0.0;
        }
        
        if (time_diff > 0.100) {  // 100ms容差，提高双摄像头同步成功率
            DEBUG_BALL_SPEED("⚠️ 双目时间戳差异过大: " + std::to_string(time_diff * 1000) + "ms，跳过此帧");
            return false;
        }
        
        // 使用左相机的时间戳作为参考（两个时间戳应该很接近）
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            left_detection_with_ts.capture_time.time_since_epoch());
        
        // 调用主要的处理方法，传入相机采集时间戳
        return processDetectionsWithTimestamp(left_detection_with_ts.detections, 
                                            right_detection_with_ts.detections, 
                                            timestamp, 
                                            left_detection_with_ts.capture_time);
        
    } catch (const std::exception& e) {
        // UTF8Utils::println("❌ processLatestDetections 错误: " + std::string(e.what()));
        return false;
    }
}

void StereoReconstructionService::updateStatistics(bool success, double processing_time_ms) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    m_stats.total_processed++;
    
    if (success) {
        m_stats.successful_reconstructions++;
    } else {
        m_stats.failed_matches++;
    }
    
    // 计算平均处理时间（移动平均）
    double alpha = 0.1; // 平滑因子
    m_stats.avg_processing_time_ms = alpha * processing_time_ms + 
                                    (1.0 - alpha) * m_stats.avg_processing_time_ms;
}

void StereoReconstructionService::reloadCalibrationData() {
    if (m_dualEye) {
        UTF8Utils::println("🔄 StereoReconstructionService: 重新加载标定数据...");
        m_dualEye->reloadCalibrationData();
        UTF8Utils::println("✅ StereoReconstructionService: 标定数据重新加载完成");
    }
}

DetectionResult StereoReconstructionService::convertYoloDetections(
    const std::map<std::string, std::vector<Yolo::Detection>>& yolo_detections
) {
    DetectionResult result;

    for (const auto& [class_name, detections] : yolo_detections) {
        for (const auto& detection : detections) {
            result.boxes.emplace_back(cv::Rect(
                cv::Point(detection.left, detection.top),
                cv::Point(detection.right, detection.bottom)
            ));
            result.confidences.push_back(detection.conf);
            result.class_ids.push_back(detection.class_id);
            result.class_names.push_back(class_name);
        }
    }

    return result;
}

void StereoReconstructionService::updateROIPredictionsUsingDualEye(const BallPosition3D& latest_position) {
    double dt = 0.01;  // 10ms间隔预测

    // 更新轨迹预测器
    cv::Point3f cv_position(
        latest_position.world_position.x,
        latest_position.world_position.y,
        latest_position.world_position.z
    );
    m_roiPredictor->updateState(cv_position, dt);

    // 预测下一帧3D位置
    cv::Point3f predicted_3d = m_roiPredictor->predictNextPosition(dt);

    // 转换为MU::Point3f格式（与DualEye接口兼容）
    MU::Point3f predicted_mu(predicted_3d.x, predicted_3d.y, predicted_3d.z);

    // 使用DualEye的projectWorldPoint方法进行投影
    auto projected_points = m_dualEye->projectWorldPoint(predicted_mu);
    cv::Point2f left_pixel = projected_points.first;
    cv::Point2f right_pixel = projected_points.second;

    // 检查可见性
    cv::Size image_size = m_dualEye->imageSize;
    bool left_visible = (left_pixel.x >= 0 && left_pixel.x < image_size.width &&
                        left_pixel.y >= 0 && left_pixel.y < image_size.height);
    bool right_visible = (right_pixel.x >= 0 && right_pixel.x < image_size.width &&
                         right_pixel.y >= 0 && right_pixel.y < image_size.height);

    // 生成左摄像头ROI
    if (left_visible) {
        cv::Rect left_roi = generateROIAroundPoint(left_pixel, 150);
        m_sharedData->setROIPrediction(1, left_roi, 0.8f);
        DEBUG_ROI("左摄像头ROI: (" + std::to_string(left_roi.x) + "," +
                 std::to_string(left_roi.y) + "," + std::to_string(left_roi.width) +
                 "x" + std::to_string(left_roi.height) + ")");
    } else {
        m_sharedData->setROIPrediction(1, cv::Rect(), 0.0f);
        DEBUG_ROI("左摄像头：球不可见，使用全画面搜索");
    }

    // 生成右摄像头ROI
    if (right_visible) {
        cv::Rect right_roi = generateROIAroundPoint(right_pixel, 150);
        m_sharedData->setROIPrediction(2, right_roi, 0.8f);
        DEBUG_ROI("右摄像头ROI: (" + std::to_string(right_roi.x) + "," +
                 std::to_string(right_roi.y) + "," + std::to_string(right_roi.width) +
                 "x" + std::to_string(right_roi.height) + ")");
    } else {
        m_sharedData->setROIPrediction(2, cv::Rect(), 0.0f);
        DEBUG_ROI("右摄像头：球不可见，使用全画面搜索");
    }

    // 更新立体匹配状态
    bool stereo_matchable = left_visible && right_visible;
    m_sharedData->setStereoMatchable(stereo_matchable);

    // 验证对极约束（复用现有的匹配阈值）
    if (stereo_matchable) {
        float y_diff = std::abs(left_pixel.y - right_pixel.y);
        if (y_diff > m_matchingThreshold) {
            DEBUG_ROI("警告：预测点对极约束验证失败，Y差异: " + std::to_string(y_diff));
        }
    }
}

bool StereoReconstructionService::processDetectionsWithTimestamp(
    const std::map<std::string, std::vector<Yolo::Detection>>& left_detections,
    const std::map<std::string, std::vector<Yolo::Detection>>& right_detections,
    std::chrono::milliseconds timestamp,
    std::chrono::high_resolution_clock::time_point capture_time
) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        std::vector<BallPosition3D> ball_positions;
        
        // 遍历所有目标类别，进行跨视图匹配
        for (const auto& class_name : m_targetClasses) {
            // 使用DualEye库进行特征匹配
            auto matched_pairs = DUE::classifyMultiple(
                left_detections, 
                right_detections, 
                class_name, 
                m_matchingThreshold
            );

            if (!matched_pairs.empty()) {
                // 进行三维重建，计算世界坐标
                auto world_points = m_dualEye->calP3inWorld(matched_pairs);
                
                // 将结果转换为BallPosition3D格式
                for (size_t i = 0; i < world_points.size(); ++i) {
                    const auto& world_point = world_points[i];
                    
                    // 计算置信度（取匹配对中的较小值）
                    float confidence = std::min<float>(
                        matched_pairs[i].confLeft, 
                        matched_pairs[i].confRight
                    );
                    
                    ball_positions.emplace_back(
                        static_cast<int>(ball_positions.size()), // 球的ID
                        world_point,
                        confidence,
                        timestamp
                    );
                }

                // 更新2D轨迹
                for (const auto& ball_match : matched_pairs) {
                    // 假设相机ID 1 是左, 2 是右
                    m_2d_trajectories[1].push_back(ball_match.uvLeft);
                    if (m_2d_trajectories[1].size() > m_max2dTrajectorySize) {
                        m_2d_trajectories[1].pop_front();
                    }

                    m_2d_trajectories[2].push_back(ball_match.uvRight);
                    if (m_2d_trajectories[2].size() > m_max2dTrajectorySize) {
                        m_2d_trajectories[2].pop_front();
                    }
                }
            }
        }
        
        // 将结果存储到SharedData中
        m_sharedData->setBallPositions3D(ball_positions);

        // 如果成功重建，计算并存储速度，并更新轨迹
        if (!ball_positions.empty()) {
            // 统计成功的3D重建次数
            m_sharedData->increment3DReconstructionCount();
            
            // 使用采集时间戳计算速度（关键修改！）
            // UTF8Utils::println("[调试] 开始计算球速 - 3D位置: (" + 
            //                   std::to_string(ball_positions[0].world_position.x) + ", " +
            //                   std::to_string(ball_positions[0].world_position.y) + ", " +
            //                   std::to_string(ball_positions[0].world_position.z) + ")");
            calculateAndStoreSpeedWithTimestamp(ball_positions[0], capture_time);

            // 更新轨迹
            if (!m_wasBallDetectedLastFrame) {
                // 如果上一帧没有球，而这一帧有，说明是新轨迹的开始
                m_currentTrajectory.clear();
                m_2d_trajectories.clear(); // 清空2D轨迹
            }
            m_wasBallDetectedLastFrame = true;

            m_currentTrajectory.push_back(ball_positions[0]);

            // 保持轨迹队列长度
            if (m_currentTrajectory.size() > m_maxTrajectorySize) {
                m_currentTrajectory.erase(m_currentTrajectory.begin());
            }

            // 将更新后的轨迹存入共享数据
            m_sharedData->setTrajectory(m_currentTrajectory);
            m_sharedData->set2dTrajectories(m_2d_trajectories);

        } else {
            // 没有检测到球时的智能处理策略
            auto current_time = std::chrono::high_resolution_clock::now();

            // 检查历史记录中最后一个点的时间
            if (!m_positionHistory.empty()) {
                auto last_detection_time = m_positionHistory.back().timestamp;
                auto time_since_last_detection = std::chrono::duration<double>(current_time - last_detection_time).count();

                // 根据实际系统性能调整球丢失的容忍时间
                const double BALL_LOSS_TOLERANCE = 1.0;  // 1秒容忍时间

                if (time_since_last_detection > BALL_LOSS_TOLERANCE) {
                    UTF8Utils::println("[调试] 球丢失超过" + std::to_string(BALL_LOSS_TOLERANCE) +
                                   "秒，但暂时保留历史记录以便球速计算。丢失时间: " +
                                   std::to_string(time_since_last_detection * 1000) + "ms");
                    // 暂时注释掉清空操作，让系统有机会积累足够历史数据
                    // m_positionHistory.clear();
                } else {
                    UTF8Utils::println("[调试] 球暂时丢失 " + std::to_string(time_since_last_detection * 1000) +
                                   "ms，保留历史记录等待重新检测");
                }
            }

            m_sharedData->setBallSpeed(0.0);
            m_wasBallDetectedLastFrame = false;
        }
        
        // 更新统计信息
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        updateStatistics(!ball_positions.empty(), duration.count() / 1000.0);
        
        return !ball_positions.empty();
        
    } catch (const std::exception& e) {
        UTF8Utils::println("❌ 带时间戳的三维重建处理错误: " + std::string(e.what()));
        
        // 更新失败统计
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        updateStatistics(false, duration.count() / 1000.0);
        
        return false;
    }
}

void StereoReconstructionService::calculateAndStoreSpeedWithTimestamp(
    const BallPosition3D& latest_position, 
    std::chrono::high_resolution_clock::time_point capture_timestamp
) {
    // 使用传入的相机采集时间戳，而不是当前处理时间
    auto current_time = capture_timestamp;
    
    // UTF8Utils::println("[调试] 进入速度计算函数 - 历史点数: " + std::to_string(m_positionHistory.size()));

    // 检测帧重复：如果新位置与最后一个位置过于接近，可能是重复帧
    if (!m_positionHistory.empty()) {
        const auto& last_pos = m_positionHistory.back().point;
        const auto& new_pos = latest_position.world_position;

        float position_change = std::sqrt(
            std::pow(new_pos.x - last_pos.x, 2) +
            std::pow(new_pos.y - last_pos.y, 2) +
            std::pow(new_pos.z - last_pos.z, 2)
        );

        // 如果位置变化小于1mm，可能是重复帧
        if (position_change < 0.001f) {
            auto time_diff = std::chrono::duration<double>(current_time - m_positionHistory.back().timestamp).count();
            DEBUG_BALL_SPEED("检测到可能的重复帧 - 位置变化: " +
                           std::to_string(position_change * 1000) + "mm, 时间间隔: " +
                           std::to_string(time_diff * 1000) + "ms");

            // 如果时间间隔也很小（<2ms），跳过此帧
            if (time_diff < 0.002) {
                DEBUG_BALL_SPEED("跳过重复帧，保持当前球速");
                // return; // 暂时注释掉，让速度计算继续进行
            }
        }
    }

    // 1. Add new point to history with camera capture timestamp
    m_positionHistory.push_back({current_time, latest_position.world_position});

    // 2. Maintain history size
    if (m_positionHistory.size() > m_historySize) {
        m_positionHistory.pop_front();
    }

    // 3. Check if we have enough data for SG filter (使用基于210FPS优化的窗口大小)
    const int optimized_window_size = m_sgWindowSize;  // 7点，基于分析的最优参数
    if (m_positionHistory.size() < optimized_window_size) {
        UTF8Utils::println("[调试] 历史数据不足: " + std::to_string(m_positionHistory.size()) +
                          "/" + std::to_string(optimized_window_size) + " 点，设置速度为0");
        m_sharedData->setBallSpeed(0.0);
        return;
    }

    // Use the most recent `optimized_window_size` points for calculation
    auto start_it = m_positionHistory.end() - optimized_window_size;

    std::vector<double> t_data, x_data, y_data, z_data;
    std::vector<double> dt_values; // 存储实际时间间隔
    t_data.reserve(optimized_window_size);
    x_data.reserve(optimized_window_size);
    y_data.reserve(optimized_window_size);
    z_data.reserve(optimized_window_size);

    auto first_timestamp = start_it->timestamp;
    for (auto it = start_it; it != m_positionHistory.end(); ++it) {
        t_data.push_back(std::chrono::duration<double>(it->timestamp - first_timestamp).count());
        x_data.push_back(it->point.x);
        y_data.push_back(it->point.y);
        z_data.push_back(it->point.z);
    }

    // 计算实际相邻帧之间的时间间隔（基于相机采集时间戳）
    for (auto it = start_it + 1; it != m_positionHistory.end(); ++it) {
        auto dt = std::chrono::duration<double>(it->timestamp - (it-1)->timestamp).count();
        dt_values.push_back(dt);
    }

    try {
        // 验证时间间隔的有效性
        if (dt_values.empty()) {
            if (DebugConfig::enable_ball_speed_debug) {
                DEBUG_BALL_SPEED("时间间隔计算失败，设置速度为0");
            }
            m_sharedData->setBallSpeed(0.0);
            // return; // 暂时注释掉，让速度计算继续进行
        }

        // 计算时间间隔统计信息
        double min_dt = *std::min_element(dt_values.begin(), dt_values.end());
        double max_dt = *std::max_element(dt_values.begin(), dt_values.end());
        double sum_dt = std::accumulate(dt_values.begin(), dt_values.end(), 0.0);
        double avg_dt = sum_dt / dt_values.size();

        // 针对210FPS相机的时间间隔处理策略（使用优化后的类常量）
        // 注意：这里使用类常量以保持一致性

        // 统计时间间隔分布
        int normal_intervals = 0;
        int acceptable_intervals = 0;
        int problematic_intervals = 0;

        for (double dt : dt_values) {
            if (dt <= MAX_NORMAL_INTERVAL) normal_intervals++;
            else if (dt <= MAX_ACCEPTABLE_INTERVAL) acceptable_intervals++;
            else problematic_intervals++;
        }

        // 如果大部分时间间隔都是异常的，说明这是球重新出现
        if (problematic_intervals > dt_values.size() / 2) {
            DEBUG_BALL_SPEED("检测到球重新出现 - 异常间隔: " + std::to_string(problematic_intervals) +
                           "/" + std::to_string(dt_values.size()) + ", 最大: " +
                           std::to_string(max_dt * 1000) + "ms, 重新开始轨迹跟踪");

            // 保留最近的2个点，丢弃旧的异常数据
            if (m_positionHistory.size() >= 2) {
                auto recent_points = std::vector<TimedPoint3f>(
                    m_positionHistory.end() - 2, m_positionHistory.end()
                );
                // m_positionHistory.clear(); // 暂时注释掉，防止历史数据清空
                for (const auto& point : recent_points) {
                    m_positionHistory.push_back(point);
                }
                DEBUG_BALL_SPEED("保留最近2个检测点，继续跟踪");
            } else {
                // m_positionHistory.clear(); // 暂时注释掉，防止历史数据清空
                m_positionHistory.push_back({current_time, latest_position.world_position});
                DEBUG_BALL_SPEED("历史数据不足，重新开始");
            }
            m_sharedData->setBallSpeed(0.0);
            // return; // 暂时注释掉，让速度计算继续进行
        }

        // 如果只有少数异常间隔，使用中位数时间间隔进行计算
        if (problematic_intervals > 0 && problematic_intervals <= dt_values.size() / 2) {
            // 计算中位数时间间隔，排除异常值
            std::vector<double> normal_dt_values;
            for (double dt : dt_values) {
                if (dt <= MAX_ACCEPTABLE_INTERVAL) {
                    normal_dt_values.push_back(dt);
                }
            }

            if (!normal_dt_values.empty()) {
                std::sort(normal_dt_values.begin(), normal_dt_values.end());
                double median_dt = normal_dt_values[normal_dt_values.size() / 2];
                avg_dt = median_dt;  // 使用中位数替代平均值

                DEBUG_BALL_SPEED("使用中位数时间间隔: " + std::to_string(median_dt * 1000) +
                               "ms (排除" + std::to_string(problematic_intervals) + "个异常值)");
            }
        }

        // 输出真实相机时间间隔信息（仅在调试模式下）
        if (DebugConfig::enable_ball_speed_debug) {
            DEBUG_BALL_SPEED("真实相机时间间隔 - 平均: " + std::to_string(avg_dt * 1000) + 
                           "ms, 预期: " + std::to_string(EXPECTED_CAMERA_INTERVAL * 1000) + "ms (210FPS)" +
                           ", 正常上限: " + std::to_string(MAX_NORMAL_INTERVAL * 1000) + "ms");
        }

        if (avg_dt < 1e-6) { // Avoid division by zero
            DEBUG_BALL_SPEED("平均时间间隔过小: " + std::to_string(avg_dt) + "s，设置速度为0");
            m_sharedData->setBallSpeed(0.0);
            // return; // 暂时注释掉，让速度计算继续进行
        }

        // 4. Compute SG coefficients for the 1st derivative (使用基于分析的最优参数)
        const int optimized_poly_order = m_sgPolyOrder;   // 2阶多项式，可检测加速度变化
        auto coeffs = SignalProcessing::compute_sg_coeffs(optimized_window_size, optimized_poly_order, 1);

        // 5. Apply filter to get derivatives (velocity components)
        // 关键修改：使用真实相机采集时间间隔计算速度
        double vx = dot_product(coeffs, x_data) / avg_dt;
        double vy = dot_product(coeffs, y_data) / avg_dt;
        double vz = dot_product(coeffs, z_data) / avg_dt;

        // 6. Calculate speed magnitude and store it
        double speed = std::sqrt(vx * vx + vy * vy + vz * vz);

        // 多级异常速度检测（基于优化后的阈值）
        if (speed > SPEED_CRITICAL_THRESHOLD) {
            DEBUG_BALL_SPEED("🚨 严重异常速度: " + std::to_string(speed) + " m/s，明确的系统错误");
        } else if (speed > SPEED_ANOMALY_THRESHOLD) {
            DEBUG_BALL_SPEED("⚠️ 异常速度: " + std::to_string(speed) + " m/s，可能的计算错误");
        } else if (speed > SPEED_WARNING_THRESHOLD) {
            DEBUG_BALL_SPEED("⚡ 警告速度: " + std::to_string(speed) + " m/s，超出常见范围");
        }

        // 详细调试信息仅在开发调试模式下显示
        if (DebugConfig::enable_ball_speed_debug) {
            DEBUG_BALL_SPEED("优化SG滤波器速度计算 - 窗口:" + std::to_string(optimized_window_size) + 
                            "点, 阶数:" + std::to_string(optimized_poly_order) + 
                            ", 时间跨度:" + std::to_string(optimized_window_size * avg_dt * 1000) + "ms" +
                            ", 平均间隔:" + std::to_string(avg_dt * 1000) + "ms" +
                            ", 速度:" + std::to_string(speed) + "m/s");
        }

        UTF8Utils::println("[调试] 成功计算球速: " + std::to_string(speed) + " m/s");
        m_sharedData->setBallSpeed(speed);

        // ROI预测逻辑
        if (m_roiEnabled && m_roiPredictor) {
            updateROIPredictionsUsingDualEye(latest_position);
        }

    } catch (const std::exception& e) {
        UTF8Utils::println(std::string("❌ SG Filter speed calculation error with timestamp: ") + e.what());
        m_sharedData->setBallSpeed(0.0);
    }
}

cv::Rect StereoReconstructionService::generateROIAroundPoint(const cv::Point2f& center, int size) {
    cv::Size image_size = m_dualEye->imageSize;

    cv::Rect roi(
        static_cast<int>(center.x - size/2),
        static_cast<int>(center.y - size/2),
        size, size
    );

    // 边界裁剪
    int max_x = static_cast<int>(image_size.width) - roi.width;
    int max_y = static_cast<int>(image_size.height) - roi.height;

    if (roi.x < 0) roi.x = 0;
    if (roi.x > max_x) roi.x = max_x;
    if (roi.y < 0) roi.y = 0;
    if (roi.y > max_y) roi.y = max_y;

    return roi;
}